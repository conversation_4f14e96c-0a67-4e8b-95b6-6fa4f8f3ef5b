import { View, Text, StyleSheet, ImageBackground } from 'react-native'
import React from 'react'
import from "@assets/images/coffee.jpg";

const app = () => {
  return (
    <View style={styles.container}>
      <ImageBackground 
        source={}
        resizeMode="cover"
        style={styles.image}>
        <Text style={styles.text}>Coffee Shop</Text>
      </ImageBackground>
    </View>
  )
}

export default app

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column'
  },
  text: {
    color: 'white',
    fontSize: 42,
    fontWeight: 'bold',
    textAlign: 'center'
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    flex: 1,
    justifyContent: 'center'
  }
})